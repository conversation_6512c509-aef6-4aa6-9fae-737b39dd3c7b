import { useState } from 'react';
import { Checkbox } from '@/libs/form/Checkbox';
import { Input } from '@/libs/form/Input';
import { CustomList } from './components/CustomList/CustomList';
import { CreateNewListButton } from './components/CreateNewListButton/CreateNewListButton';
import { ProductType } from '@/types';
import { useListFiltering } from './hooks/useListFiltering';
import mockData from './mockData.json';
import { ClinicShoppingListType } from '../types';

export const CustomLists = ({
  setActiveTab,
}: {
  setActiveTab: (tab: number) => void;
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [customLists, setCustomLists] = useState<ClinicShoppingListType[]>(
    mockData.shoppingLists,
  );

  // const { filteredLists } = useListFiltering({
  //   lists: customLists,
  //   searchQuery,
  // });

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
  };

  const handleCreateList = (listName: string) => {
    console.log('Creating new list:', listName);
  };

  const handleReorderProducts = (
    listName: string,
    newProducts: ProductType[],
  ) => {
    // setCustomLists((prevLists) =>
    //   prevLists.map((list) =>
    //     list.name === listName ? { ...list, products: newProducts } : list,
    //   ),
    // );
  };

  return (
    <div>
      <div className="mt-4 mb-6 flex items-center justify-between">
        <Checkbox
          checked={false}
          onChange={() => {}}
          label="Select all your lists"
        />
        <div className="flex items-center gap-4">
          <form onSubmit={handleSearchSubmit}>
            <Input
              placeholder="Search lists or item"
              value={searchQuery}
              onChange={(e) =>
                setSearchQuery(e.target.value.toLocaleLowerCase())
              }
              size="sm"
              className="w-64"
            />
          </form>
          <CreateNewListButton onCreateList={handleCreateList} />
        </div>
      </div>
      <div className="space-y-4">
        {customLists.map((list) => (
          <CustomList
            key={list.id}
            setActiveTab={setActiveTab}
            list={list}
            searchQuery={searchQuery}
            onReorderProducts={handleReorderProducts}
          />
        ))}
      </div>
    </div>
  );
};
