import { ProductType, StockStatusType, VendorType } from '@/types';

export type PreviouslyPurchasedResponse = {
  data: PreviouslyPurchasedItemType[];
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta: {
    currentPage: number;
    from: number;
    lastPage: number;
    path: string;
    perPage: number;
    to: number;
    total: number;
    links: Array<{
      url: string | null;
      label: string;
      page: number | null;
      active: boolean;
    }>;
  };
};

export type FetchPreviouslyPurchasedParams = {
  search?: string;
  perPage?: number;
  page?: number;
};

export type FetchPreviouslyPurchasedProps = {
  params: FetchPreviouslyPurchasedParams;
  beforeStart?: VoidFunction;
  onSuccess?: (data: {
    items: PreviouslyPurchasedItemType[];
    total: number;
  }) => void;
  onError?: VoidFunction;
  afterDone?: VoidFunction;
};

export type PreviouslyPurchasedItemType = {
  productOfferId: string;
  unitPrice: string;
  lastOrderedQuantity: number;
  lastOrderedAt: string;
  orderCount: number;
  product: Omit<
    ProductType,
    'isFavorite' | 'manufacturer' | 'manufacturerSku' | 'description'
  >;
  vendor: VendorType;
  stockStatus: StockStatusType;
  increments: number;
  isPurchasable: boolean;
};
