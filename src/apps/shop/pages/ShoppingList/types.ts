import { ProductType, StockStatusType, VendorType, OfferType } from '@/types';
import { UserType, ClinicType } from '@/types/common';

export type PreviouslyPurchasedResponse = {
  data: PreviouslyPurchasedItemType[];
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta: {
    currentPage: number;
    from: number;
    lastPage: number;
    path: string;
    perPage: number;
    to: number;
    total: number;
    links: Array<{
      url: string | null;
      label: string;
      page: number | null;
      active: boolean;
    }>;
  };
};

export type FetchPreviouslyPurchasedParams = {
  search?: string;
  perPage?: number;
  page?: number;
};

export type FetchPreviouslyPurchasedProps = {
  params: FetchPreviouslyPurchasedParams;
  beforeStart?: VoidFunction;
  onSuccess?: (data: {
    items: PreviouslyPurchasedItemType[];
    total: number;
  }) => void;
  onError?: VoidFunction;
  afterDone?: VoidFunction;
};

export type PreviouslyPurchasedItemType = {
  productOfferId: string;
  unitPrice: string;
  lastOrderedQuantity: number;
  lastOrderedAt: string;
  orderCount: number;
  product: Omit<
    ProductType,
    'isFavorite' | 'manufacturer' | 'manufacturerSku' | 'description'
  >;
  vendor: VendorType;
  stockStatus: StockStatusType;
  increments: number;
  isPurchasable: boolean;
};

// Shopping List Types
export interface ClinicShoppingListType {
  id: string;
  clinicId: string;
  createdByUserId: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  // Relationships
  clinic?: ClinicType;
  createdByUser?: UserType;
  items?: ShoppingListItemType[];
}

export interface ShoppingListItemType {
  id: string;
  clinicShoppingListId: string;
  productOfferId: string;
  createdByUserId: string;
  quantity: number;
  color: string | null;
  label: string | null;
  rank: number;
  createdAt: string;
  updatedAt: string;
  // Relationships
  clinicShoppingList?: ClinicShoppingListType;
  product: ProductType;
  createdByUser?: UserType;
}

// API Request Types
export interface CreateShoppingListRequest {
  clinicId: string;
  title: string;
}

export interface UpdateShoppingListRequest {
  title?: string;
}

export interface CreateShoppingListItemRequest {
  clinicShoppingListId: string;
  productOfferId: string;
  quantity: number;
  color?: string | null;
  label?: string | null;
  rank?: number;
}

export interface UpdateShoppingListItemRequest {
  quantity?: number;
  color?: string | null;
  label?: string | null;
  rank?: number;
}

// API Response Types
export interface ShoppingListResponse {
  data: ClinicShoppingListType[];
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta: {
    currentPage: number;
    from: number;
    lastPage: number;
    path: string;
    perPage: number;
    to: number;
    total: number;
    links: Array<{
      url: string | null;
      label: string;
      page: number | null;
      active: boolean;
    }>;
  };
}

export interface ShoppingListItemsResponse {
  data: ShoppingListItemType[];
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta: {
    currentPage: number;
    from: number;
    lastPage: number;
    path: string;
    perPage: number;
    to: number;
    total: number;
    links: Array<{
      url: string | null;
      label: string;
      page: number | null;
      active: boolean;
    }>;
  };
}

// API Parameters Types
export interface FetchShoppingListsParams {
  search?: string;
  perPage?: number;
  page?: number;
  clinicId?: string;
}

export interface FetchShoppingListItemsParams {
  search?: string;
  perPage?: number;
  page?: number;
  shoppingListId: string;
}
